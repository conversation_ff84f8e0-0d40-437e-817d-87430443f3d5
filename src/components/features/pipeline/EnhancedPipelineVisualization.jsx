/**
 * Enhanced Pipeline Visualization Component
 * Supports both compact popup view and expanded layouts (right-side panel or full-screen modal)
 * Features live console logs spanning vertically across whole available space
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { X, Maximize2, Minimize2, PanelRight, Fullscreen, Play, Pause, RotateCcw } from 'lucide-react';
import PipelineStepCard from './PipelineStepCard.jsx';
import LiveConsoleLogger from './LiveConsoleLogger.jsx';
import { PIPELINE_STEPS, STEP_STATUS, getNextAvailableSteps } from '../../../core/config/pipelineSteps.js';
import { documentProcessingPipeline } from '../../../services/DocumentProcessingPipeline.js';

// Layout modes
const LAYOUT_MODES = {
  COMPACT: 'compact',        // Original squeezed layout in popup
  RIGHT_PANEL: 'right_panel', // Right-side panel layout
  FULL_SCREEN: 'full_screen'  // Full-screen modal layout
};

const EnhancedPipelineVisualization = ({
  file = null,
  isProcessing = false,
  onProcessingChange,
  onStepComplete,
  onError,
  autoRun = false,
  initialLayout = LAYOUT_MODES.COMPACT
}) => {
  const [layoutMode, setLayoutMode] = useState(initialLayout);
  const [pipelineState, setPipelineState] = useState({
    steps: {},
    currentStep: null,
    completedSteps: [],
    errors: {},
    results: {},
    timings: {},
    overallProgress: 0
  });

  const [consoleLogs, setConsoleLogs] = useState([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const modalRef = useRef(null);

  // Add log entry to console with live updates
  const addLog = useCallback((level, message, stepName = null, data = null) => {
    const logEntry = {
      id: `${Date.now()}-${Math.random()}`,
      timestamp: new Date().toISOString(),
      level,
      message,
      stepName,
      data
    };
    setConsoleLogs(prev => [...prev, logEntry]);
  }, []);

  // Clear console logs
  const clearLogs = useCallback(() => {
    setConsoleLogs([]);
  }, []);

  // Export console logs
  const exportLogs = useCallback(() => {
    const logData = consoleLogs.map(log => ({
      timestamp: log.timestamp,
      level: log.level,
      step: log.stepName || 'system',
      message: log.message,
      data: log.data
    }));

    const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `pipeline-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [consoleLogs]);

  // Handle layout mode changes
  const handleLayoutChange = useCallback((newMode) => {
    setLayoutMode(newMode);
    if (newMode === LAYOUT_MODES.FULL_SCREEN) {
      setIsExpanded(true);
    } else if (newMode === LAYOUT_MODES.COMPACT) {
      setIsExpanded(false);
    }
  }, []);

  // Handle escape key for full-screen mode
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && layoutMode === LAYOUT_MODES.FULL_SCREEN) {
        handleLayoutChange(LAYOUT_MODES.COMPACT);
      }
    };

    if (layoutMode === LAYOUT_MODES.FULL_SCREEN) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [layoutMode, handleLayoutChange]);

  // Update step state
  const updateStepState = useCallback((stepId, updates) => {
    setPipelineState(prev => ({
      ...prev,
      steps: {
        ...prev.steps,
        [stepId]: {
          ...prev.steps[stepId],
          ...updates
        }
      }
    }));
  }, []);

  // Calculate overall progress
  const calculateOverallProgress = useCallback((completedSteps, currentStep, currentProgress) => {
    const totalSteps = PIPELINE_STEPS.length;
    const completedCount = completedSteps.length;
    const currentStepProgress = currentProgress || 0;

    return Math.round(((completedCount + (currentStepProgress / 100)) / totalSteps) * 100);
  }, []);

  // Run full pipeline
  const runFullPipeline = useCallback(async () => {
    if (!file || isProcessing) return;

    try {
      addLog('info', '🚀 Starting full pipeline execution', null, { fileName: file.name });
      onProcessingChange?.(true);

      // Get API key from environment variables
      const apiKey = window.__MVAT_ENV__?.DEEPSEEK_API_KEY || process.env.DEEPSEEK_API_KEY;
      if (!apiKey) {
        addLog('warning', '⚠️ No DeepSeek API key found in environment variables');
      } else {
        addLog('info', '✅ DeepSeek API key loaded from environment');
      }

      const progressCallback = (stepId, progress, message) => {
        updateStepState(stepId, {
          status: STEP_STATUS.RUNNING,
          progress: progress
        });

        setPipelineState(prev => ({
          ...prev,
          currentStep: stepId,
          overallProgress: calculateOverallProgress(prev.completedSteps, stepId, progress)
        }));

        if (message) {
          addLog('info', message, stepId, { progress });
        }
      };

      // Execute pipeline steps sequentially
      for (const step of PIPELINE_STEPS) {
        try {
          addLog('info', `🔄 Starting step: ${step.name}`, step.id);
          updateStepState(step.id, { status: STEP_STATUS.RUNNING, progress: 0 });

          const startTime = Date.now();
          const result = await runSingleStep(step.id, file, { progressCallback, apiKey });
          const endTime = Date.now();
          const timing = endTime - startTime;

          updateStepState(step.id, {
            status: STEP_STATUS.COMPLETED,
            progress: 100,
            result: result,
            timing: timing
          });

          setPipelineState(prev => ({
            ...prev,
            completedSteps: [...prev.completedSteps, step.id],
            results: { ...prev.results, [step.id]: result },
            timings: { ...prev.timings, [step.id]: timing }
          }));

          addLog('success', `✅ Completed step: ${step.name} (${timing}ms)`, step.id, result);
          onStepComplete?.(result);

        } catch (error) {
          const errorMessage = error.message || 'Unknown error';
          addLog('error', `❌ Failed step: ${step.name} - ${errorMessage}`, step.id, { error: errorMessage });

          updateStepState(step.id, {
            status: STEP_STATUS.ERROR,
            error: errorMessage
          });

          setPipelineState(prev => ({
            ...prev,
            errors: { ...prev.errors, [step.id]: errorMessage }
          }));

          onError?.(errorMessage);
          break; // Stop pipeline on error
        }
      }

      addLog('info', '🎉 Pipeline execution completed');

    } catch (error) {
      const errorMessage = error.message || 'Pipeline execution failed';
      addLog('error', `❌ Pipeline failed: ${errorMessage}`, null, { error: errorMessage });
      onError?.(errorMessage);
    } finally {
      onProcessingChange?.(false);
      setPipelineState(prev => ({ ...prev, currentStep: null }));
    }
  }, [file, isProcessing, onProcessingChange, onStepComplete, onError, addLog, updateStepState, calculateOverallProgress]);

  // Run single pipeline step
  const runSingleStep = useCallback(async (stepId, file, options = {}) => {
    const { progressCallback, apiKey } = options;

    let result;
    switch (stepId) {
      case 'pdf_extraction':
        result = await documentProcessingPipeline.runPdfExtraction(file, { progressCallback });
        break;
      case 'deepseek_analysis':
        if (!apiKey) {
          throw new Error('DeepSeek API key required for analysis');
        }
        result = await documentProcessingPipeline.runDeepSeekAnalysis(file, {
          progressCallback,
          apiKey,
          language: 'pol',
          companyInfo: {
            name: window.__MVAT_ENV__?.COMPANY_NAME || 'MVAT Solutions'
          }
        });
        break;
      case 'rag_document_linking':
        result = await documentProcessingPipeline.runRagDocumentLinking(file, { progressCallback });
        break;
      case 'ocr_structural_reference':
        result = await documentProcessingPipeline.runOcrStructuralReference(file, { progressCallback });
        break;
      case 'final_output_generation':
        result = await documentProcessingPipeline.runFinalOutputGeneration(file, { progressCallback });
        break;
      default:
        throw new Error(`Unknown step: ${stepId}`);
    }

    return result;
  }, []);

  // Handle step actions
  const handleStepAction = useCallback(async (stepId, actionType) => {
    if (actionType === 'rerun') {
      try {
        addLog('info', `🔄 Rerunning step: ${stepId}`);
        const apiKey = window.__MVAT_ENV__?.DEEPSEEK_API_KEY || process.env.DEEPSEEK_API_KEY;
        const result = await runSingleStep(stepId, file, { apiKey });
        addLog('success', `✅ Step rerun completed: ${stepId}`, stepId, result);
      } catch (error) {
        addLog('error', `❌ Step rerun failed: ${stepId} - ${error.message}`, stepId);
      }
    }
  }, [file, addLog, runSingleStep]);

  // Auto-run pipeline when file is provided
  useEffect(() => {
    if (file && autoRun && !isProcessing) {
      runFullPipeline();
    }
  }, [file, autoRun, isProcessing, runFullPipeline]);

  // Render pipeline arrows
  const renderPipelineArrow = (index) => {
    if (index >= PIPELINE_STEPS.length - 1) return null;

    return (
      <div className="flex justify-center py-2">
        <div className="w-0.5 h-4 bg-gray-300 flex items-center justify-center">
          <div className="w-2 h-2 border-l-2 border-b-2 border-gray-400 transform rotate-45 -translate-y-1"></div>
        </div>
      </div>
    );
  };

  // Render layout controls
  const renderLayoutControls = () => (
    <div className="flex items-center space-x-2">
      <button
        onClick={() => handleLayoutChange(LAYOUT_MODES.COMPACT)}
        className={`p-2 rounded ${layoutMode === LAYOUT_MODES.COMPACT ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
        title="Compact View"
      >
        <Minimize2 size={16} />
      </button>
      <button
        onClick={() => handleLayoutChange(LAYOUT_MODES.RIGHT_PANEL)}
        className={`p-2 rounded ${layoutMode === LAYOUT_MODES.RIGHT_PANEL ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
        title="Right Panel View"
      >
        <PanelRight size={16} />
      </button>
      <button
        onClick={() => handleLayoutChange(LAYOUT_MODES.FULL_SCREEN)}
        className={`p-2 rounded ${layoutMode === LAYOUT_MODES.FULL_SCREEN ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
        title="Full Screen View"
      >
        <Fullscreen size={16} />
      </button>
    </div>
  );

  // Render compact layout (original squeezed view)
  const renderCompactLayout = () => (
    <div className="w-full h-full max-h-96 overflow-hidden">
      {/* Header with layout controls */}
      <div className="flex items-center justify-between mb-4 p-2 bg-gray-50 rounded">
        <h3 className="text-sm font-semibold text-gray-900">Multi-Step Pipeline</h3>
        <div className="flex items-center space-x-2">
          {renderLayoutControls()}
          {file && (
            <button
              onClick={runFullPipeline}
              disabled={isProcessing}
              className="px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700 disabled:bg-gray-400"
            >
              {isProcessing ? '⏳' : '🚀'}
            </button>
          )}
        </div>
      </div>

      {/* Progress bar */}
      {isProcessing && (
        <div className="mb-2">
          <div className="w-full bg-gray-200 rounded-full h-1">
            <div
              className="bg-blue-500 h-1 rounded-full transition-all duration-300"
              style={{ width: `${pipelineState.overallProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Compact pipeline steps */}
      <div className="space-y-1 max-h-64 overflow-y-auto">
        {PIPELINE_STEPS.map((step, index) => (
          <div key={step.id} className="text-xs">
            <div className="flex items-center space-x-2 p-2 bg-white rounded border">
              <span className="text-sm">{step.icon}</span>
              <span className="flex-1 truncate">{step.name}</span>
              <span className={`text-xs ${
                pipelineState.steps[step.id]?.status === STEP_STATUS.COMPLETED ? 'text-green-600' :
                pipelineState.steps[step.id]?.status === STEP_STATUS.RUNNING ? 'text-blue-600' :
                pipelineState.steps[step.id]?.status === STEP_STATUS.ERROR ? 'text-red-600' : 'text-gray-400'
              }`}>
                {pipelineState.steps[step.id]?.status === STEP_STATUS.COMPLETED ? '✅' :
                 pipelineState.steps[step.id]?.status === STEP_STATUS.RUNNING ? '⏳' :
                 pipelineState.steps[step.id]?.status === STEP_STATUS.ERROR ? '❌' : '⏸️'}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Render right panel layout
  const renderRightPanelLayout = () => (
    <div className="fixed inset-y-0 right-0 w-2/3 bg-white shadow-2xl z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <h3 className="text-lg font-semibold text-gray-900">Multi-Step Processing Pipeline</h3>
        <div className="flex items-center space-x-2">
          {renderLayoutControls()}
          <button
            onClick={() => handleLayoutChange(LAYOUT_MODES.COMPACT)}
            className="p-2 text-gray-500 hover:text-gray-700"
            title="Close Panel"
          >
            <X size={20} />
          </button>
        </div>
      </div>

      {/* Progress bar */}
      {isProcessing && (
        <div className="px-4 py-2 border-b border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span>Overall Progress</span>
            <span>{pipelineState.overallProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${pipelineState.overallProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Main content - Split layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left: Pipeline Steps */}
        <div className="w-1/2 border-r border-gray-200 overflow-y-auto">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-medium text-gray-900">Pipeline Steps</h4>
              {file && (
                <button
                  onClick={runFullPipeline}
                  disabled={isProcessing}
                  className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:bg-gray-400"
                >
                  {isProcessing ? '⏳ Processing...' : '🚀 Run Pipeline'}
                </button>
              )}
            </div>
            <div className="space-y-3">
              {PIPELINE_STEPS.map((step, index) => (
                <div key={step.id}>
                  <PipelineStepCard
                    step={step}
                    status={pipelineState.steps[step.id]?.status}
                    progress={pipelineState.steps[step.id]?.progress}
                    result={pipelineState.steps[step.id]?.result}
                    error={pipelineState.steps[step.id]?.error}
                    timing={pipelineState.steps[step.id]?.timing}
                    onAction={handleStepAction}
                    isActive={pipelineState.currentStep === step.id}
                    showExpandableData={true}
                  />
                  {renderPipelineArrow(index)}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right: Live Console Logs - Full Height */}
        <div className="w-1/2 flex flex-col">
          <LiveConsoleLogger
            logs={consoleLogs}
            isProcessing={isProcessing}
            onClear={clearLogs}
            maxLogs={1000}
            autoScroll={true}
            className="flex-1 h-full"
          />
        </div>
      </div>
    </div>
  );

  // Render full screen layout
  const renderFullScreenLayout = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-2xl w-11/12 h-5/6 flex flex-col max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50 rounded-t-lg">
          <div>
            <h3 className="text-xl font-semibold text-gray-900">Multi-Step Processing Pipeline</h3>
            <p className="text-sm text-gray-600 mt-1">
              {file ? `Processing: ${file.name}` : 'No file selected'}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {renderLayoutControls()}
            <button
              onClick={() => handleLayoutChange(LAYOUT_MODES.COMPACT)}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
              title="Close Full Screen"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Progress bar */}
        {isProcessing && (
          <div className="px-6 py-3 border-b border-gray-200 bg-blue-50">
            <div className="flex items-center justify-between text-sm text-gray-700 mb-2">
              <span className="font-medium">Overall Progress</span>
              <span className="font-mono">{pipelineState.overallProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-blue-500 h-3 rounded-full transition-all duration-300 relative overflow-hidden"
                style={{ width: `${pipelineState.overallProgress}%` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
              </div>
            </div>
          </div>
        )}

        {/* Main content - Split layout with more space */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left: Pipeline Steps - Wider in full screen */}
          <div className="w-2/5 border-r border-gray-200 overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h4 className="text-lg font-medium text-gray-900">Pipeline Steps</h4>
                {file && (
                  <button
                    onClick={runFullPipeline}
                    disabled={isProcessing}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 flex items-center space-x-2"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Processing...</span>
                      </>
                    ) : (
                      <>
                        <Play size={16} />
                        <span>Run Full Pipeline</span>
                      </>
                    )}
                  </button>
                )}
              </div>
              <div className="space-y-4">
                {PIPELINE_STEPS.map((step, index) => (
                  <div key={step.id}>
                    <PipelineStepCard
                      step={step}
                      status={pipelineState.steps[step.id]?.status}
                      progress={pipelineState.steps[step.id]?.progress}
                      result={pipelineState.steps[step.id]?.result}
                      error={pipelineState.steps[step.id]?.error}
                      timing={pipelineState.steps[step.id]?.timing}
                      onAction={handleStepAction}
                      isActive={pipelineState.currentStep === step.id}
                      showExpandableData={true}
                    />
                    {renderPipelineArrow(index)}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right: Live Console Logs - Maximum vertical space */}
          <div className="w-3/5 flex flex-col">
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <h4 className="text-lg font-medium text-gray-900">Live Console Logs</h4>
              <p className="text-sm text-gray-600">Real-time processing logs with full vertical space</p>
            </div>
            <div className="flex-1 overflow-hidden">
              <LiveConsoleLogger
                logs={consoleLogs}
                isProcessing={isProcessing}
                onClear={clearLogs}
                onExport={exportLogs}
                maxLogs={2000}
                autoScroll={true}
                className="h-full"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Main render logic
  if (layoutMode === LAYOUT_MODES.COMPACT) {
    return renderCompactLayout();
  } else if (layoutMode === LAYOUT_MODES.RIGHT_PANEL) {
    return renderRightPanelLayout();
  } else if (layoutMode === LAYOUT_MODES.FULL_SCREEN) {
    return renderFullScreenLayout();
  }

  return renderCompactLayout();
};

export default EnhancedPipelineVisualization;
export { LAYOUT_MODES };
